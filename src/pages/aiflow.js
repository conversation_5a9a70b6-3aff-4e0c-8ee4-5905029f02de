import React, { useState } from 'react';
import clsx from 'clsx';
import Link from '@docusaurus/Link';
import Layout from '@theme/Layout';
import Translate, { translate } from '@docusaurus/Translate';

import Heading from '@theme/Heading';
import styles from './index.module.css';
import Footer from '../components/Footer';
import FAQSection from '../components/FAQSection';

import AIToolsSection from '../components/AIToolsSection';
import TestimonialsSection from '../components/TestimonialsSection';
import ImageModal from '../components/ImageModal';
import GoogleAccountAnalytics from '../components/GoogleAccountAnalytics';
import IntroSection from '../components/IntroSection';
import CTASection from '../components/CTASection';
import ThinkingMattersSection from '../components/ThinkingMattersSection';
import StructuredData from '../components/StructuredData';
import ComparisonSection from '../components/ComparisonSection';
import SocialProofSection from '../components/SocialProofSection';

function AIFlowHeader({ setShowImageSrc, toApp }) {
  return (
    <section id="hero" className={clsx(styles.hero, styles.pageSection)} style={{
      backgroundColor: '#f8f8f8',
      backgroundImage: 'radial-gradient(#ccc 1px, transparent 1px)',
      backgroundSize: '20px 20px',
      padding: '40px 20px'
    }}>
      <div className="container" style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <Heading as="h1" style={{ fontSize: '2.8rem', textAlign: 'center', maxWidth: '800px' }}>
          <Translate id="aiflow.masthead.title_2">
            Unlock Your Cognitive Potential with Visualized ChatGPT
          </Translate>
        </Heading>
        <p className={styles.heroSubtitle} style={{ fontSize: '1.4rem', maxWidth: '800px', textAlign: 'center' }}>
          <Translate id="aiflow.masthead.subtitle">
            Visualize Ideas, Solve Problems, and Learn Faster with Integrated Brainstorming, Mind Mapping, Critical Thinking, and Creative Thinking Tools
          </Translate>
        </p>

        <div className={styles.heroButtons} style={{ marginTop: '2rem' }}>
          <Link
            className={clsx('button', styles.btn)}
            to="#"
            onClick={() => toApp()}
            style={{ fontSize: '1.2rem', padding: '12px 30px' }}
          >
            <Translate id="aiflow.masthead.cta">Start Free Trial</Translate>
          </Link>
          <Link
            className={clsx('button', styles.btnSecondary)}
            to="#thinking-methods"
            style={{ fontSize: '1.2rem', padding: '12px 30px', marginLeft: '15px' }}
          >
            <Translate id="aiflow.masthead.learn_more">Explore Features</Translate>
          </Link>
        </div>
        {/* Product Hunt Notification */}
        <div style={{
          backgroundColor: '#f8f9fa',
          color: '#333',
          border: '2px solid blueviolet',
          padding: '15px 20px',
          borderRadius: '10px',
          marginTop: '30px',
          textAlign: 'center',
          maxWidth: '800px'
        }}>
          <div style={{ marginBottom: '10px', fontSize: '1.1rem', fontWeight: 'bold', color: '#495057' }}>
            <Translate id="aiflow.product_hunt.announcement">
              🎉 NEW: AI Mindmap Chrome Extension Now Available!
            </Translate>
          </div>
          <div style={{ marginBottom: '15px', fontSize: '0.95rem', color: '#6c757d' }}>
            <Translate id="aiflow.product_hunt.description">
              We've launched the Chrome extension version of AIFlow - AI Mindmap. Support us on Product Hunt!
            </Translate>
          </div>
          <div style={{ display: 'flex', gap: '15px', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
            <a href="https://www.producthunt.com/products/funblocks-aiflow?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-ai&#0045;mindmap&#0045;extension" target="_blank">
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=982383&theme=neutral&t=1750776163796"
                alt="AI&#0032;Mindmap&#0032;Extension - Transform&#0032;Web&#0032;page&#0044;&#0032;PDF&#0044;&#0032;YT&#0032;Video&#0032;into&#0032;Mind&#0032;Maps&#0032;Instantly | Product Hunt"
                style={{ width: '250px', height: '54px', display: 'flex' }}
                width="250"
                height="54"
              />
            </a>
            <Link
              to="/ai-mindmap"
              style={{
                display: 'inline-block',
                backgroundColor: '#007bff',
                color: 'white',
                padding: '10px 24px',
                borderRadius: '6px',
                textDecoration: 'none',
                fontSize: '1rem',
                fontWeight: 'bold',
                transition: 'background-color 0.3s ease',
                height: '50px',
                lineHeight: '30px',
                boxSizing: 'border-box'
              }}
              onMouseEnter={(e) => e.target.style.backgroundColor = '#0056b3'}
              onMouseLeave={(e) => e.target.style.backgroundColor = '#007bff'}
            >
              <Translate id="aiflow.product_hunt.learn_more">Learn More</Translate>
            </Link>
          </div>
        </div>

        <div className={styles.thinkingMethodsContainer}>
          <div className={styles.thinkingMethodItem}>
            <span className={styles.thinkingMethodIcon}>⚡</span>
            <span className={styles.thinkingMethodText}>
              <Translate id="aiflow.thinking_methods.brainstorming">Brainstorming</Translate>
            </span>
          </div>
          <div className={styles.thinkingMethodItem}>
            <span className={styles.thinkingMethodIcon}>🗺️</span>
            <span className={styles.thinkingMethodText}>
              <Translate id="aiflow.thinking_methods.mind_mapping">Mind Mapping</Translate>
            </span>
          </div>
          <div className={styles.thinkingMethodItem}>
            <span className={styles.thinkingMethodIcon}>🔍</span>
            <span className={styles.thinkingMethodText}>
              <Translate id="aiflow.thinking_methods.critical_thinking">Critical Thinking</Translate>
            </span>
          </div>
          <div className={styles.thinkingMethodItem}>
            <span className={styles.thinkingMethodIcon}>💡</span>
            <span className={styles.thinkingMethodText}>
              <Translate id="aiflow.thinking_methods.creative_thinking">Creative Thinking</Translate>
            </span>
          </div>
        </div>
        <p style={{ marginTop: '20px', fontStyle: 'italic' }}>
          <Translate id="homepage.thinking_matters.title">Your Thinking Matters in the Age of AI</Translate>
        </p>
      </div>
    </section>
  );
}

function FeaturesSection({ setShowImageSrc }) {
  const features = [
    {
      // icon: '🧩',
      nameId: 'aiflow.features.item1.name',
      descriptionId: 'aiflow.features.item1.description',
      listItems: [
        { id: 'aiflow.features.item1.li1', text: 'Break complex problems into manageable parts' },
        { id: 'aiflow.features.item1.li2', text: 'Rapidly identify key elements and potential challenges' },
        { id: 'aiflow.features.item1.li3', text: 'Simplify the research process through a divide-and-conquer approach' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_breakdown.png",
      imageAlt: "Illustration of AI-powered problem breakdown process",
    },
    {
      // icon: '💡',
      nameId: 'aiflow.features.item2.name',
      descriptionId: 'aiflow.features.item2.description',
      listItems: [
        { id: 'aiflow.features.item2.li1', text: 'Generate multiple perspectives on any topic' },
        { id: 'aiflow.features.item2.li2', text: 'Discover connections and insights you might have missed' },
        { id: 'aiflow.features.item2.li3', text: 'Let AI suggest unexpected angles and approaches' },
        { id: 'aiflow.features.item2.li4', text: 'Let AI suggest unexpected angles and approaches' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_expansion.png",
      imageAlt: "Visualization of AI-assisted thought expansion and idea generation",
    },
    {
      nameId: 'aiflow.features.item3.name',
      descriptionId: 'aiflow.features.item3.description',
      listItems: [
        { id: 'aiflow.features.item3.li1', text: 'Image: AI analysis of visuals, from art to whiteboards' },
        { id: 'aiflow.features.item3.li2', text: 'Note: Quick idea capture with AI-powered expansion' },
        { id: 'aiflow.features.item3.li3', text: 'Task list: AI-enhanced task management and prioritization' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_optimize_prompt.png",
      imageAlt: "Showcase of diverse content types supported by AI assistants",
    },

    {
      nameId: 'aiflow.features.item4.name',
      descriptionId: 'aiflow.features.item4.description',
      listItems: [
        { id: 'aiflow.features.item4.li1', text: 'Image: AI analysis of visuals, from art to whiteboards' },
        { id: 'aiflow.features.item4.li2', text: 'Note: Quick idea capture with AI-powered expansion' },
        { id: 'aiflow.features.item4.li3', text: 'Task list: AI-enhanced task management and prioritization' },
        { id: 'aiflow.features.item4.li4', text: 'Link: Web content integration for research and inspiration' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_image.png",
      imageAlt: "Showcase of diverse content types supported by AI assistants",
    },
    {
      nameId: 'aiflow.features.item5.name',
      descriptionId: 'aiflow.features.item5.description',
      listItems: [
        { id: 'aiflow.features.item5.li1', text: 'Organize related concepts into cohesive clusters for enhanced comprehension' },
        { id: 'aiflow.features.item5.li2', text: 'Leverage AI assistant support for analysis and generation' },
        { id: 'aiflow.features.item5.li3', text: 'Synthesize diverse ideas into comprehensive solutions with one-click' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_group_nodes.png",
      imageAlt: "Demonstration of AI-powered group nodes for organizing and synthesizing ideas",
    },
    {
      nameId: 'aiflow.features.item6.name',
      descriptionId: 'aiflow.features.item6.description',
      listItems: [
        { id: 'aiflow.features.item6.li1', text: 'Provide intelligent analysis and suggestions to enhance thinking quality' },
        { id: 'aiflow.features.item6.li2', text: 'Automatically generate high-quality content, saving time and effort' },
        { id: 'aiflow.features.item6.li3', text: 'Support personalized AI instructions to meet specific needs' },
      ],
      imageSrc: "/img/portfolio/thumbnails/aiflow_notes.png",
      imageAlt: "Overview of the all-in-one AI assistant features and customizable prompts",
    },
  ];

  return (
    <section id="features" className={clsx(styles.featureSection)} style={{ backgroundColor: '#F0FFF0' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.features.title">Key Features</Translate>
        </Heading>

        {features.map((feature, index) => (
          <div className={styles.featureGrid} style={{
            marginTop: index > 0 ? '3rem' : '0',
            flexDirection: index % 2 === 0 ? 'row-reverse' : 'row', // Change layout based on index
          }} key={index}>
            <div style={{ cursor: 'pointer', flex: 5 }}>
              <img
                className={styles.featureImage}
                onClick={() => setShowImageSrc(feature.imageSrc.replace('thumbnails', 'fullsize'))}
                id={`aiflow-${index + 1}`}
                alt={feature.imageAlt}
                src={feature.imageSrc}
              />
            </div>
            <div className={styles.featureContent} style={{ flex: 3 }}>
              {feature.icon && <div className={styles.benefitIcon}>{feature.icon}</div>}
              <Heading as="h3">
                <Translate id={feature.nameId}>{feature.nameId}</Translate>
              </Heading>
              <p>
                <Translate id={feature.descriptionId}>{feature.descriptionId}</Translate>
              </p>
              <ul className={styles.featureList}>
                {feature.listItems.map((item, itemIndex) => (
                  <li key={itemIndex}>
                    <Translate id={item.id}>{item.text}</Translate>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

function AIBrainstormingSection({ setShowImageSrc }) {
  return (
    <section id="ai-powered-brainstorming" className={styles.featureSection}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.ai-powered-brainstorming.title">AI-Powered Brainstorming</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.ai-powered-brainstorming.subtitle">Boost creativity with AI-assisted classic thinking models</Translate>
        </p>

        <div className={styles.featureGrid}>
          <div style={{ cursor: 'pointer', flex: 4 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_productivity.png")}
              id="aiflow-brainstorming"
              alt="AI-powered brainstorming with classic thinking models"
              src="/img/portfolio/thumbnails/aiflow_productivity.png"
            />
          </div>
          <div className={styles.featureContent} style={{
            flex: 2
          }}>
            <Heading as="h3">
              <Translate id="aiflow.ai-powered-brainstorming.classic_models.title">Ideation with Classic Thinking Models</Translate>
            </Heading>
            <p>
              <Translate id="aiflow.ai-powered-brainstorming.classic_models.subtitle">AI-powered brainstorming with structured frameworks</Translate>
            </p>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.classic_models.m1">Six Thinking Hats</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.classic_models.m2">SWOT Analysis</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.classic_models.m3">McKinsey Method</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.classic_models.m4">First Principles</Translate>
              </li>
              {/* <li>
                <Translate id="aiflow.ai-powered-brainstorming.classic_models.m5">SWOT Analysis</Translate>
              </li> */}
            </ul>

            <Heading as="h3" style={{ paddingTop: '10px' }}>
              <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.title">Ideation with Classic Thinking Models</Translate>
            </Heading>
            <p>
              <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.subtitle">AI-powered brainstorming with structured frameworks</Translate>
            </p>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.m1">Presentation Slides</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.m2">Solution Document</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.m3">Infographics</Translate>
              </li>
              <li>
                <Translate id="aiflow.ai-powered-brainstorming.oneclick_generation.m4">Images</Translate>
              </li>

            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

function BookInsightsSection({ setShowImageSrc }) {
  return (
    <section id="book-insights" className={styles.featureSection} style={{ backgroundColor: 'cornsilk' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.book-insights.title">
            Deep Learning from Books & Movies with AI Mind Maps
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.book-insights.subtitle">
            Transform Complex Works into Clear, Actionable Insights
          </Translate>
        </p>

        <div className={styles.featureGrid}>
          <div className={styles.featureContent} style={{
            flex: 2,
          }}>
            <i className="fas fa-4x fa-book-reader text-primary mb-4" />
            <Heading as="h3">
              <Translate id="aiflow.book-insights.features.title">Unlock Knowledge Efficiently</Translate>
            </Heading>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aiflow.book-insights.features.li1">
                  Extract key concepts and themes instantly
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.book-insights.features.li2">
                  Visualize complex relationships and character dynamics
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.book-insights.features.li3">
                  Generate comprehensive chapter summaries
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.book-insights.features.li4">
                  Explore related topics and deeper insights
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.book-insights.features.li5">
                  Create personalized learning paths
                </Translate>
              </li>
            </ul>
          </div>

          <div style={{ cursor: 'pointer', flex: 3 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_book.png")}
              id="aiflow-brainstorming"
              alt="AI-powered mind mapping for book analysis and learning"
              src="/img/portfolio/thumbnails/aiflow_book.png"
            />
          </div>
        </div>
      </div>
    </section>
  );
}

function UseCasesSection({ setShowImageSrc }) {
  const useCases = [
    {
      icon: '📚',
      titleId: 'aiflow.use-cases.case1.title',
      descriptionId: 'aiflow.use-cases.case1.description',
    },
    {
      icon: '🎯',
      titleId: 'aiflow.use-cases.case2.title',
      descriptionId: 'aiflow.use-cases.case2.description',
    },
    {
      icon: '🔍',
      titleId: 'aiflow.use-cases.case3.title',
      descriptionId: 'aiflow.use-cases.case3.description',
    },
    {
      icon: '⚙️',
      titleId: 'aiflow.use-cases.case4.title',
      descriptionId: 'aiflow.use-cases.case4.description',
    },
    {
      icon: '📝',
      titleId: 'aiflow.use-cases.case5.title',
      descriptionId: 'aiflow.use-cases.case5.description',
    },
    {
      icon: '⚡',
      titleId: 'aiflow.use-cases.case6.title',
      descriptionId: 'aiflow.use-cases.case6.description',
    },
  ];

  return (
    <section id="use-cases" className={styles.useCases}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.use-cases.title">Use Cases</Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.use-cases.description">AIFlow adapts to diverse knowledge work scenarios, enhancing your thinking process from exploration to execution</Translate>
        </p>

        <div
          className={styles.featureGrid}
          style={{
            flexDirection: 'row',
          }}
        >

          <div className={styles.featureContent} style={{ flex: 3 }}>
            {useCases.map((useCase, index) => (

              <div key={index}>
                <span style={{ fontSize: 20, fontWeight: 'bold' }}>
                  <Translate id={useCase.titleId}>Multidimensional Thinking</Translate>
                </span>
                <p style={{ marginBottom: 5 }}>
                  <Translate id={useCase.descriptionId}>Analyze problems comprehensively with multidimensional mind maps</Translate>
                </p>
              </div>
            ))}
          </div>

          <div style={{ cursor: 'pointer', flex: 5 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_learning.png")}
              alt={"AIFlow boost creativity and productivity under multi scenarios"}
              src={"/img/portfolio/thumbnails/aiflow_learning.png"}
            />
          </div>
        </div>
      </div>
    </section>
  );
}

// 思维方法比较部分
function ThinkingMethodsComparisonSection({ setShowImageSrc }) {
  const thinkingMethods = [
    {
      name: 'Brainstorming',
      description: 'Generate a wide range of ideas without judgment',
      aiFlowFeatures: [
        'AI-assisted idea generation',
        'Classic thinking models integration',
        'Visual organization of ideas',
        'One-click expansion of concepts'
      ],
      imageSrc: '/img/portfolio/fullsize/aiflow_brainstorming_prompt.png',
      imageAlt: 'AI-powered brainstorming with FunBlocks AIFlow'
    },
    {
      name: 'Mind Mapping',
      description: 'Organize information visually to see connections',
      aiFlowFeatures: [
        'Infinite canvas for complex maps',
        'Hierarchical node structure',
        'AI-generated connections',
        'Visual customization options'
      ],
      imageSrc: '/img/portfolio/thumbnails/aiflow_book.png',
      imageAlt: 'Mind mapping capabilities in FunBlocks AIFlow'
    },
    {
      name: 'Critical Thinking',
      description: 'Analyze information objectively to form reasoned judgments',
      aiFlowFeatures: [
        'Cognitive bias identification',
        'Assumption testing tools',
        'Multiple perspective analysis',
        'Structured evaluation frameworks'
      ],
      imageSrc: '/img/portfolio/thumbnails/aiflow_critical_tools.png',
      imageAlt: 'Critical thinking tools in FunBlocks AIFlow'
    },
    {
      name: 'Creative Thinking',
      description: 'Develop innovative solutions and unique perspectives',
      aiFlowFeatures: [
        'Lateral thinking prompts',
        'Analogical reasoning tools',
        'Constraint removal exercises',
        'Idea combination techniques'
      ],
      imageSrc: '/img/portfolio/thumbnails/aiflow_productivity.png',
      imageAlt: 'Creative thinking enhancement with FunBlocks AIFlow'
    }
  ];

  return (
    <section id="thinking-methods" className={styles.featureSection} style={{ backgroundColor: 'lavender' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.thinking-methods.title">
            Enhance Your Thinking with AIFlow
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.thinking-methods.description">
            FunBlocks AIFlow integrates powerful thinking methodologies with AI to enhance your cognitive abilities
          </Translate>
        </p>

        {thinkingMethods.map((method, index) => (
          <div
            key={index}
            className={styles.featureGrid}
            style={{
              marginTop: index > 0 ? '3rem' : '0',
              flexDirection: index % 2 === 0 ? 'row' : 'row-reverse',
            }}
          >
            <div style={{ cursor: 'pointer', flex: 4 }}>
              <img
                className={styles.featureImage}
                onClick={() => setShowImageSrc(method.imageSrc.replace('thumbnails', 'fullsize'))}
                alt={method.imageAlt}
                src={method.imageSrc}
              />
            </div>
            <div className={styles.featureContent} style={{ flex: 3 }}>
              <Heading as="h3">
                <Translate id={`aiflow.thinking-methods.${method.name.toLowerCase().replace(' ', '_')}.title`}>
                  {method.name}
                </Translate>
              </Heading>
              <p>
                <Translate id={`aiflow.thinking-methods.${method.name.toLowerCase().replace(' ', '_')}.description`}>
                  {method.description}
                </Translate>
              </p>
              <Heading as="h4" style={{ fontSize: '1.1rem', marginTop: '1rem' }}>
                <Translate id="aiflow.thinking-methods.aiflow-features">
                  AIFlow Features:
                </Translate>
              </Heading>
              <ul className={styles.featureList}>
                {method.aiFlowFeatures.map((feature, featureIndex) => (
                  <li key={featureIndex}>
                    <Translate
                      id={`aiflow.thinking-methods.${method.name.toLowerCase().replace(' ', '_')}.feature${featureIndex + 1}`}
                    >
                      {feature}
                    </Translate>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

// 案例研究部分
function CaseStudiesSection({ setShowImageSrc }) {
  return (
    <section id="case-studies" className={styles.featureSection} style={{ backgroundColor: '#FFF8DC' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.case-studies.title">
            AIFlow in Action: Real-World Applications
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.case-studies.description">
            See how professionals and students use FunBlocks AIFlow to enhance their thinking and productivity
          </Translate>
        </p>

        <div className={styles.featureGrid}>
          <div style={{ cursor: 'pointer', flex: 2 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc('/img/portfolio/fullsize/aitools_brainstorming_marketing.png')}
              alt="Case study of AIFlow being used for marketing project planning"
              src="/img/portfolio/thumbnails/aitools_brainstorming_marketing.png"
            />
          </div>
          <div className={styles.featureContent} style={{ flex: 3, maxWidth: '60%' }}>
            <Heading as="h3">
              <Translate id="aiflow.case-studies.education.title">
                Educational Excellence
              </Translate>
            </Heading>
            <p>
              <Translate id="aiflow.case-studies.education.description">
                A university professor used AIFlow to help students visualize complex concepts in cognitive psychology.
                Students created mind maps to connect theories, research findings, and real-world applications,
                resulting in 40% better comprehension compared to traditional note-taking.
              </Translate>
            </p>
            <Heading as="h3" style={{ marginTop: '1.5rem' }}>
              <Translate id="aiflow.case-studies.business.title">
                Business Innovation
              </Translate>
            </Heading>
            <p>
              <Translate id="aiflow.case-studies.business.description">
                A product development team at a tech startup used AIFlow's brainstorming and critical thinking
                tools to identify market gaps and develop innovative solutions. The visual approach helped
                them reduce planning time by 60% while generating 3x more viable product ideas.
              </Translate>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

// 教育资源部分
// AI Mindmap Extension 介绍部分
function AIMindmapExtensionSection({ setShowImageSrc }) {
  return (
    <section id="ai-mindmap-extension" className={styles.featureSection} style={{ backgroundColor: '#E6F3FF' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.ai_mindmap_extension.title">
            AI Mindmap Chrome Extension
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.ai_mindmap_extension.subtitle">
            Transform any web content into mind maps instantly. Your gateway to FunBlocks AIFlow.
          </Translate>
        </p>

        <div className={styles.featureGrid}>
          <div style={{ cursor: 'pointer', flex: 4 }}>
            <img
              className={styles.featureImage}
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/ai-mindmap-extension-demo.png")}
              alt="AI Mindmap Extension interface demonstration"
              src="/img/portfolio/thumbnails/ai-mindmap-extension-demo.png"
            />
          </div>
          <div className={styles.featureContent} style={{ flex: 3 }}>
            <Heading as="h3">
              <Translate id="aiflow.ai_mindmap_extension.features.title">
                Key Features
              </Translate>
            </Heading>
            <ul className={styles.featureList}>
              <li>
                <Translate id="aiflow.ai_mindmap_extension.features.web_content">
                  One-click mind mapping from web pages and PDFs
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.ai_mindmap_extension.features.youtube">
                  YouTube video transcript analysis and visualization
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.ai_mindmap_extension.features.brainstorming">
                  AI-powered brainstorming with classic thinking models
                </Translate>
              </li>
              <li>
                <Translate id="aiflow.ai_mindmap_extension.features.integration">
                  Seamless integration with FunBlocks AIFlow
                </Translate>
              </li>
            </ul>

            <div style={{ marginTop: '20px' }}>
              <Link
                className={clsx('button', styles.btn)}
                to="/ai-mindmap"
                style={{ fontSize: '1.1rem', padding: '10px 25px', marginRight: '15px' }}
              >
                <Translate id="aiflow.ai_mindmap_extension.learn_more">Learn More</Translate>
              </Link>
              <Link
                className={clsx('button', styles.btnSecondary)}
                to="https://chromewebstore.google.com/detail/ai-mindmap-mind-mapping-g/nlalnbdblcdgnammbelmmngehcloildo"
                target="_blank"
                style={{ fontSize: '1.1rem', padding: '10px 25px' }}
              >
                <Translate id="aiflow.ai_mindmap_extension.download">Download Extension</Translate>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function EducationalResourcesSection() {
  const resources = [
    {
      title: 'Brainstorming Techniques',
      description: 'Learn effective brainstorming methods enhanced by AI',
      link: '/docs/aiflow-tricks-and-tips/Brainstorming',
      icon: '⚡'
    },
    {
      title: 'Mind Mapping Mastery',
      description: 'Discover how to create powerful mind maps for any purpose',
      link: '/thinking-matters/intro/mind-mapping',
      icon: '🗺️'
    },
    {
      title: 'Critical Thinking Skills',
      description: 'Enhance your analytical abilities with structured approaches',
      link: '/docs/aiflow-tricks-and-tips/Critical-Thinking',
      icon: '🔍'
    },
    {
      title: 'Creative Problem Solving',
      description: 'Break through creative blocks with innovative techniques',
      link: '/thinking-matters/intro/creative-thinking',
      icon: '💡'
    },
    {
      title: 'Mental Models Library',
      description: 'Access powerful thinking frameworks for complex problems',
      link: '/thinking-matters/intro/mental-models',
      icon: '🧩'
    },
    {
      title: 'Integrated Workflow Guide',
      description: 'See how all thinking methods work together in AIFlow',
      link: '/thinking-matters/intro/funblocks-aiflow-in-action-integrated-workflow-from-problem-to-solution',
      icon: '⚙️'
    }
  ];

  return (
    <section id="educational-resources" className={styles.featureSection} style={{ backgroundColor: '#F0F8FF' }}>
      <div className="container">
        <Heading as="h2" className={styles.sectionTitle}>
          <Translate id="aiflow.educational-resources.title">
            Educational Resources
          </Translate>
        </Heading>
        <p className={styles.sectionDescription}>
          <Translate id="aiflow.educational-resources.description">
            Enhance your thinking skills with our comprehensive guides and tutorials
          </Translate>
        </p>

        <div className={styles.resourcesGrid}>
          {resources.map((resource, index) => (
            <div key={index} style={{
              backgroundColor: 'white',
              borderRadius: '10px',
              padding: '1.5rem',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              transition: 'transform 0.3s, box-shadow 0.3s',
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}
            className={styles.resourceCard}
            >
              <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>{resource.icon}</div>
              <Heading as="h3" style={{ fontSize: '1.3rem', marginBottom: '0.5rem' }}>
                <Translate id={`aiflow.educational-resources.resource${index + 1}.title`}>
                  {resource.title}
                </Translate>
              </Heading>
              <p style={{ flex: 1 }}>
                <Translate id={`aiflow.educational-resources.resource${index + 1}.description`}>
                  {resource.description}
                </Translate>
              </p>
              <Link
                to={resource.link}
                className={styles.resourceLink}
                style={{
                  display: 'inline-block',
                  marginTop: '1rem',
                  color: 'var(--primary)',
                  fontWeight: 'bold'
                }}
              >
                <Translate id="aiflow.educational-resources.learn_more">Learn More</Translate> →
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

export default function AIFlow() {
  const [showImageSrc, setShowImageSrc] = useState(null);

  function getDomain() {
    if (!window.location.hostname.includes('funblocks')) {
      return 'funblocks.net';
    }
    return window.location.hostname.replace('www.', '');
  }

  function openUrl(url) {
    let newTab = window.open();
    newTab.location.href = url;
  }

  function toApp() {
    let url = `https://app.${getDomain()}/#/login?source=flow`;
    openUrl(url);
  }

  const testimonials_avatars = ["👩‍🏫", "👨‍💼", "👩‍💼", "👨‍🎓", "👩‍🎓", "👨‍🏫"];

  // 自定义比较数据
  const comparisonData = [
    {
      feature: translate({
        id: 'aiflow.comparison.feature1',
        message: 'Brainstorming Capabilities'
      }),
      funblocks: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      chatgpt: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      }),
      miro: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      }),
      notion: '❌'
    },
    {
      feature: translate({
        id: 'aiflow.comparison.feature2',
        message: 'Mind Mapping'
      }),
      funblocks: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      chatgpt: '❌',
      miro: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      }),
      notion: '❌'
    },
    {
      feature: translate({
        id: 'aiflow.comparison.feature3',
        message: 'Critical Thinking Tools'
      }),
      funblocks: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      chatgpt: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      }),
      miro: '❌',
      notion: '❌'
    },
    {
      feature: translate({
        id: 'aiflow.comparison.feature4',
        message: 'Creative Thinking Frameworks'
      }),
      funblocks: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      chatgpt: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      }),
      miro: '❌',
      notion: '❌'
    },
    {
      feature: translate({
        id: 'aiflow.comparison.feature5',
        message: 'Visual Thinking Integration'
      }),
      funblocks: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      chatgpt: '❌',
      miro: translate({
        id: 'aiflow.comparison.advanced',
        message: '✅ Advanced'
      }),
      notion: translate({
        id: 'aiflow.comparison.basic',
        message: '✅ Basic'
      })
    }
  ];

  return (
    <Layout
      title={translate({
        id: 'aiflow.head.title',
        message: 'Visualized Chat with AI, Best for Brainstorming, Mind Mapping, Critical & Creative Thinking'
      })}
      description={translate({
        id: 'aiflow.head.description',
        message: 'FunBlocks AIFlow: Enhance your thinking with AI-powered brainstorming, mind mapping, critical thinking, and creative thinking tools. Visualize ideas, solve problems & learn faster with GPT-4 & Claude LLM. Free trial available!'
      })}
    >
      <StructuredData />
      <AIFlowHeader setShowImageSrc={setShowImageSrc} toApp={toApp} />
      <main>
        <IntroSection
          page="aiflow"
          feature={'intro'}
          pointNos={[1, 2, 3, 4]}
          style={{backgroundColor: 'lightcyan'}}
          imageElement={<div style={{ flex: 4, cursor: 'pointer' }}>
            <img
              className={styles.featureImage}
              alt="FunBlocks AIFlow benefits compared to ChatGPT"
              src="/img/portfolio/thumbnails/aiflow_benefits.png"
              onClick={() => setShowImageSrc("/img/portfolio/fullsize/aiflow_benefits.png")}
            />
          </div>}
        />
        <ThinkingMethodsComparisonSection setShowImageSrc={setShowImageSrc} />
        <FeaturesSection setShowImageSrc={setShowImageSrc} />
        <AIBrainstormingSection setShowImageSrc={setShowImageSrc} />
        <BookInsightsSection setShowImageSrc={setShowImageSrc} />
        <IntroSection
          page="aiflow"
          feature={'explore-with-ai'}
          pointNos={[1, 2, 3, 4]}
          style={{backgroundColor: '#F0FFF0'}}
          imageElement={<div style={{ cursor: 'pointer', flex: 3.5 }}>
          <img
            className={styles.featureImage}
            onClick={() => setShowImageSrc('/img/portfolio/fullsize/aiflow_related_question.png')}
            alt={'AI-guided topic discovery and exploration with AIFlow'}
            src={'/img/portfolio/thumbnails/aiflow_related_question.png'}
          />
        </div>}
        />
        <ComparisonSection
          page="aiflow"
          customData={comparisonData}
          titleTranslateId="aiflow.comparison.title"
          descriptionTranslateId="aiflow.comparison.description"
          competitors={{
            funblocks: { label: 'FunBlocks AIFlow', isHighlighted: true },
            chatgpt: { label: 'ChatGPT', isHighlighted: false },
            miro: { label: 'Miro', isHighlighted: false },
            notion: { label: 'Notion', isHighlighted: false }
          }}
        />
        <CaseStudiesSection setShowImageSrc={setShowImageSrc} />
        <UseCasesSection setShowImageSrc={setShowImageSrc} />
        <AIMindmapExtensionSection setShowImageSrc={setShowImageSrc} />
        <AIToolsSection />
        <EducationalResourcesSection />
        <SocialProofSection page={'aiflow'} showProductHuntBadges={true} />
        <TestimonialsSection avatars={testimonials_avatars} page={'aiflow'} />
        <ThinkingMattersSection />
        <CTASection toApp={toApp} page={'aiflow'} />
        <FAQSection
          page={'aiflow'}
          faqIds={[
            'q1', 'q2', 'q3', 'q4', 'q5', 'q6', 'q7', 'q8',
            'q9', 'q10', 'q11', 'q12'
          ]}
        />
      </main>
      <Footer />

      {/* Image Modal */}
      {showImageSrc && <ImageModal imageSrc={showImageSrc} setImageSrc={setShowImageSrc} />}
      <GoogleAccountAnalytics page={'aiflow'}/>
    </Layout>
  );
}